# 🔐 **VERIFAI - Anti-Counterfeit Product Verification System**
## **Final Year Project **

[![Blockchain](https://img.shields.io/badge/Blockchain-Ethereum-blue)](https://ethereum.org/)
[![Smart Contract](https://img.shields.io/badge/Smart%20Contract-Solidity-green)](https://soliditylang.org/)
[![Frontend](https://img.shields.io/badge/Frontend-React-61DAFB)](https://reactjs.org/)
[![Backend](https://img.shields.io/badge/Backend-Node.js-339933)](https://nodejs.org/)
[![Database](https://img.shields.io/badge/Database-PostgreSQL-336791)](https://postgresql.org/)
[![Blockchain Network](https://img.shields.io/badge/Network-Ganache-orange)](https://trufflesuite.com/ganache/)

---

## 🏗️ **System Architecture & Technology Stack**

### **Frontend Layer**
- **React.js** with Material UI and Vite
- **Web3.js** for blockchain interaction
- **JWT** authentication with session management
- **Glassmorphism UI** with futuristic design elements
- **Responsive design** with WCAG 2.1 AA compliance

### **Backend Layer**
- **Node.js** with Express framework
- **PostgreSQL** database with optimized queries
- **JWT** with forced password change security
- **Rate limiting** and security middleware
- **Environment variables** for configuration

### **Blockchain Layer**
- **Ethereum** smart contracts (Solidity)
- **Ganache** local blockchain environment *(preferred over Hardhat)*
- **MetaMask** integration for wallet connectivity
- **Gas-optimized** contract deployment

---

## 🚀 **Quick Start Guide (4-Terminal Setup)**

### **Prerequisites**
- Node.js (v14+)
- PostgreSQL database
- MetaMask browser extension
- **Ganache** (GUI or CLI)

### **🔧 Step 1: Ganache Blockchain Setup (CRITICAL FIRST STEP)**

#### **Option A: Ganache GUI (Recommended for Stability)**
1. Download from [trufflesuite.com/ganache](https://trufflesuite.com/ganache/)
2. Open Ganache Desktop → Click "Quickstart"
3. **Verify Configuration**:
   - **Port**: `7545`
   - **Chain ID**: `1337`
   - **Accounts**: 10 accounts with 100 ETH each
   - **RPC URL**: `http://127.0.0.1:7545`

#### **Option B: Ganache CLI**
```bash
npm install -g ganache-cli
ganache-cli -p 7545 -i 1337 --accounts 10 --defaultBalanceEther 100
# Keep this terminal running
```

### **🦊 Step 2: MetaMask Configuration**
1. **Add Ganache Network**:
   ```
   Network Name: Ganache Local
   RPC URL: http://127.0.0.1:7545
   Chain ID: 1337
   Currency Symbol: ETH
   ```

2. **Import Ganache Account**:
   - Ganache → Click key icon next to any account
   - Copy private key → MetaMask → Import Account
   - Verify ~100 ETH balance appears

### **📜 Step 3: Smart Contract Deployment**
```bash
# Terminal 2
cd verifai-smartcontract-solidity
npm install
npx hardhat compile
node scripts/deploy-ganache.js
# Expected: ✅ Contract deployed + Registration/Retrieval tests passed
```

### **🖥️ Step 4: Backend Server**
```bash
# Terminal 3
cd Verifai-backend
npm install
npm start
# Expected: "Server is running on port 3000"
```

### **🌐 Step 5: Frontend Application**
```bash
# Terminal 4
cd verifai-frontend-react
npm install
npm run vite
# Expected: "Local: http://localhost:5173/"
```

---

## 👥 **User Roles & Supply Chain Logic**

### **🏭 Manufacturer**
- Register new products on blockchain
- Generate QR codes for authentication
- **Restriction**: Can only set `isSold: false`
- Monitor product lifecycle through dashboard

### **🚚 Supplier**
- Verify and update product location/status
- Manage inventory of received products
- **Restriction**: Cannot change `isSold` status
- Track supply chain movements

### **🏪 Retailer**
- Update product status upon receipt
- **Exclusive Permission**: Only role that can set `isSold: true`
- Record customer purchases
- Final point in supply chain verification

### **👤 Consumer**
- Scan QR codes for instant verification
- View complete product history
- Report suspicious products
- Access authentication without registration

---

## 🔒 **Security & Performance Features**

### **Authentication & Authorization**
- JWT-based session management with refresh tokens
- **Forced password change** for admin-created accounts
- Role-based access control with strict permissions
- Rate limiting to prevent brute force attacks
- Secure session storage with localStorage

### **Blockchain Security**
- Immutable product registration records
- Gas-optimized smart contract functions
- **Ganache stability** over Hardhat complexity
- MetaMask integration for secure transactions

### **Performance Metrics (Ganache Implementation)**
- **Registration Time**: 3-8 seconds *(improved from 15-30+ with Hardhat)*
- **Success Rate**: 95%+ *(up from ~70% with Hardhat)*
- **QR Scanning**: Instant recognition
- **Product Retrieval**: <2 seconds
- **System Stability**: No hanging or timeout issues

---

## 🛠️ **Troubleshooting & Diagnostics**

### **Common Issues & Quick Fixes**

#### **❌ Registration Hangs or Fails**
```bash
# Solution Checklist:
1. Verify Ganache running on port 7545
2. Check MetaMask connected to "Ganache Local"
3. Ensure sufficient ETH balance (should show ~100 ETH)
4. Reset MetaMask account if needed
```

#### **❌ QR Scanner Shows "Fake Product"**
```bash
# Contract Address Mismatch - Redeploy:
cd verifai-smartcontract-solidity
node scripts/deploy-ganache.js
# This auto-updates all frontend components
```

#### **❌ MetaMask Shows 0 ETH**
```bash
# Network Configuration Issue:
1. Confirm Ganache running on port 7545
2. Switch MetaMask to "Ganache Local" network
3. Re-import Ganache account private key
```

### **🔍 Quick Health Check**
```bash
cd verifai-smartcontract-solidity
node scripts/simple-test.js
# Expected: ✅ Registration and retrieval both successful
```

---

## 📁 **Project Structure**

```
verifai/
├── 📂 verifai-frontend-react/        # React UI with Material Design
├── 📂 Verifai-backend/               # Node.js API server
├── 📂 verifai-smartcontract-solidity/ # Ethereum contracts
├── 📂 verifai-db/                    # Database scripts
├── 📄 COMPLETE_SOLUTION_GUIDE.md     # Comprehensive documentation
├── 📄 GANACHE_SETUP_COMPLETE.md      # Ganache-specific setup
└── 📄 README.md                      # This file
```

---

## 🎨 **Design System**

### **UI/UX Framework**
- **Glassmorphism effects** with neon accents
- **Professional typography** and gradient elements
- **Smooth animations** and micro-interactions
- **Responsive design** for all device types
- **WCAG 2.1 AA compliance** for accessibility
- **Consistent design patterns** across all user roles

### **Color Palette & Branding**
- Professional futuristic theme
- High contrast for accessibility
- Consistent "Verifai" branding
- Material UI foundation with custom theming

---

## 🏆 **Why Ganache Over Hardhat**

### **Stability Advantages**
- ✅ **Consistent network** with no forking complexities
- ✅ **Predictable accounts** with deterministic setup
- ✅ **Fast transactions** with instant mining
- ✅ **Better MetaMask support** and connection handling

### **Development Benefits**
- ✅ **Visual interface** for blockchain inspection
- ✅ **Reliable gas estimation** without failures
- ✅ **Consistent state** persistence between restarts
- ✅ **Clear transaction logs** and error messages

---

---

## 📄 **License & Acknowledgements**

**MIT License** - See LICENSE file for details

**Special Thanks:**
- Global Rigorous Aura Success (M) Sdn Bhd for project sponsorship
- University faculty for academic guidance and support
- Ganache team for providing stable blockchain development environment
- Open-source community for tools and libraries

---

