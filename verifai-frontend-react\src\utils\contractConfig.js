// Auto-generated contract configuration for Ganache
export const CONTRACT_ADDRESS = "******************************************";
export const NETWORK_CONFIG = {
    chainId: 1337,
    chainName: "Ganache Local",
    rpcUrl: "http://127.0.0.1:7545",
    nativeCurrency: {
        name: "Ethereum",
        symbol: "ETH",
        decimals: 18
    }
};

export const GANACHE_ACCOUNTS = [
    "******************************************", // Account 0
    "******************************************", // Account 1
    "******************************************", // Account 2
    "******************************************", // Account 3
    "******************************************"  // Account 4
];

export default {
    CONTRACT_ADDRESS,
    NETWORK_CONFIG,
    GANACHE_ACCOUNTS
};
